// NOTICE: This file is generated by Rollup. To modify it,
// please instead edit the ESM counterpart and rebuild with Rollup (npm run build).
'use strict';

const preprocessWarnings = require('./preprocessWarnings.cjs');

/**
 * @type {import('stylelint').Formatter}
 */
function compactFormatter(results) {
	const lines = results.flatMap((result) => {
		const { warnings } = preprocessWarnings(result);

		return warnings.map(
			(warning) =>
				`${result.source}: ` +
				`line ${warning.line}, ` +
				`col ${warning.column}, ` +
				`${warning.severity} - ` +
				`${warning.text}`,
		);
	});

	lines.push('');

	return lines.join('\n');
}

module.exports = compactFormatter;
