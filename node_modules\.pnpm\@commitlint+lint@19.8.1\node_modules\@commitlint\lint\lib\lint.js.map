{"version": 3, "file": "lint.js", "sourceRoot": "", "sources": ["../src/lint.ts"], "names": [], "mappings": "AAAA,OAAO,IAAI,MAAM,WAAW,CAAC;AAC7B,OAAO,SAAS,MAAM,wBAAwB,CAAC;AAC/C,OAAO,KAAK,MAAM,mBAAmB,CAAC;AACtC,OAAO,YAAY,MAAM,mBAAmB,CAAC;AAU7C,OAAO,EAAE,kBAAkB,EAAE,MAAM,mBAAmB,CAAC;AAEvD,OAAO,EAAE,kBAAkB,EAAE,MAAM,qBAAqB,CAAC;AAEzD,MAAM,CAAC,OAAO,CAAC,KAAK,UAAU,IAAI,CACjC,OAAe,EACf,cAA+B,EAC/B,OAAqB;IAErB,MAAM,IAAI,GAAG,OAAO;QACnB,CAAC,CAAC,OAAO;QACT,CAAC,CAAC,EAAE,cAAc,EAAE,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC;IACrD,MAAM,WAAW,GAAG,cAAc,IAAI,EAAE,CAAC;IAEzC,+BAA+B;IAC/B,IACC,SAAS,CAAC,OAAO,EAAE,EAAE,QAAQ,EAAE,IAAI,CAAC,cAAc,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,EAC3E,CAAC;QACF,OAAO;YACN,KAAK,EAAE,IAAI;YACX,MAAM,EAAE,EAAE;YACV,QAAQ,EAAE,EAAE;YACZ,KAAK,EAAE,OAAO;SACd,CAAC;IACH,CAAC;IAED,2BAA2B;IAC3B,MAAM,MAAM,GACX,OAAO,KAAK,EAAE;QACb,CAAC,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE;QAC5C,CAAC,CAAC,MAAM,KAAK,CAAC,OAAO,EAAE,SAAS,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;IAErD,IACC,MAAM,CAAC,MAAM,KAAK,IAAI;QACtB,MAAM,CAAC,IAAI,KAAK,IAAI;QACpB,MAAM,CAAC,MAAM,KAAK,IAAI,EACrB,CAAC;QACF,wBAAwB;QACxB,OAAO;YACN,KAAK,EAAE,IAAI;YACX,MAAM,EAAE,EAAE;YACV,QAAQ,EAAE,EAAE;YACZ,KAAK,EAAE,OAAO;SACd,CAAC;IACH,CAAC;IAED,MAAM,QAAQ,GAA2C,IAAI,GAAG,CAC/D,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAC5B,CAAC;IAEF,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;QAClB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;YAC9C,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;gBAClB,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE,CAC7C,QAAQ,CAAC,GAAG,CAAC,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAC5C,CAAC;YACH,CAAC;QACF,CAAC,CAAC,CAAC;IACJ,CAAC;IAED,6BAA6B;IAC7B,MAAM,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,MAAM,CAC9C,CAAC,IAAI,EAAE,EAAE,CAAC,OAAO,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,UAAU,CAClD,CAAC;IAEF,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACxB,MAAM,KAAK,GAAG,CAAC,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;QACnC,MAAM,IAAI,UAAU,CACnB;YACC,uCAAuC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG;YAC5D,wBAAwB,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG;SAC3C,CAAC,IAAI,CAAC,IAAI,CAAC,CACZ,CAAC;IACH,CAAC;IAED,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC;SACzC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,EAAE;QACvB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;YAC5B,OAAO,IAAI,KAAK,CACf,mBAAmB,IAAI,4BAA4B,IAAI,CAAC,OAAO,CAC9D,MAAM,CACN,YAAY,OAAO,MAAM,EAAE,CAC5B,CAAC;QACH,CAAC;QAED,MAAM,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC;QAEvB,IAAI,KAAK,KAAK,kBAAkB,CAAC,QAAQ,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAClE,OAAO,IAAI,CAAC;QACb,CAAC;QAED,MAAM,CAAC,EAAE,IAAI,CAAC,GAAG,MAAM,CAAC;QAExB,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC;YAC/C,OAAO,IAAI,KAAK,CACf,kBAAkB,IAAI,6BAA6B,IAAI,CAAC,OAAO,CAC9D,KAAK,CACL,YAAY,OAAO,KAAK,EAAE,CAC3B,CAAC;QACH,CAAC;QAED,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC5C,OAAO,IAAI,KAAK,CACf,mBAAmB,IAAI,wCAAwC,IAAI,CAAC,OAAO,CAC1E,MAAM,CACN,cAAc,MAAM,CAAC,MAAM,EAAE,CAC9B,CAAC;QACH,CAAC;QAED,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC;YAC5B,OAAO,IAAI,UAAU,CACpB,kBAAkB,IAAI,sCAAsC,IAAI,CAAC,OAAO,CACvE,KAAK,CACL,EAAE,CACH,CAAC;QACH,CAAC;QAED,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;YAC9B,OAAO,IAAI,KAAK,CACf,sBAAsB,IAAI,6BAA6B,IAAI,CAAC,OAAO,CAClE,IAAI,CACJ,YAAY,OAAO,IAAI,EAAE,CAC1B,CAAC;QACH,CAAC;QAED,IAAI,IAAI,KAAK,OAAO,IAAI,IAAI,KAAK,QAAQ,EAAE,CAAC;YAC3C,OAAO,IAAI,KAAK,CACf,sBAAsB,IAAI,0CAA0C,IAAI,CAAC,OAAO,CAC/E,IAAI,CACJ,EAAE,CACH,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC;IACb,CAAC,CAAC;SACD,MAAM,CAAC,CAAC,IAAI,EAAiB,EAAE,CAAC,IAAI,YAAY,KAAK,CAAC,CAAC;IAEzD,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACxB,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAC3D,CAAC;IAED,6BAA6B;IAC7B,MAAM,cAAc,GAAG,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC;QACjD,4BAA4B;SAC3B,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;SAClE,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;QACpB,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,KAAK,CAAC;QAC7B,MAAM,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC,GAAG,MAAO,CAAC,CAAC,EAAE;QAExC,MAAM,IAAI,GAAG,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAEhC,IAAI,CAAC,IAAI,EAAE,CAAC;YACX,MAAM,IAAI,KAAK,CAAC,0CAA0C,IAAI,EAAE,CAAC,CAAC;QACnE,CAAC;QAED,MAAM,cAAc,GAAG,IAAqB,CAAC;QAC7C,MAAM,CAAC,KAAK,EAAE,OAAO,CAAC,GAAG,MAAM,cAAc,CAAC,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;QAEnE,OAAO;YACN,KAAK;YACL,KAAK;YACL,IAAI;YACJ,OAAO;SACP,CAAC;IACH,CAAC,CAAC,CAAC;IAEJ,MAAM,OAAO,GAAG,CAAC,MAAM,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC,CAAC,MAAM,CACzD,CAAC,MAAM,EAA6B,EAAE,CAAC,MAAM,KAAK,IAAI,CACtD,CAAC;IAEF,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAC5B,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,KAAK,KAAK,kBAAkB,CAAC,KAAK,IAAI,CAAC,MAAM,CAAC,KAAK,CACtE,CAAC;IACF,MAAM,QAAQ,GAAG,OAAO,CAAC,MAAM,CAC9B,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,KAAK,KAAK,kBAAkB,CAAC,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CACxE,CAAC;IAEF,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,KAAK,CAAC,CAAC;IAElC,OAAO;QACN,KAAK;QACL,MAAM;QACN,QAAQ;QACR,KAAK,EAAE,kBAAkB,CAAC,MAAM,CAAC;KACjC,CAAC;AACH,CAAC"}