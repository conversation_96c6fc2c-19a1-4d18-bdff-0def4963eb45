{"name": "@commitlint/config-validator", "type": "module", "version": "19.8.1", "description": "config validator for commitlint.config.js", "main": "lib/validate.js", "types": "lib/validate.d.ts", "files": ["lib/"], "scripts": {"deps": "dep-check", "pkg": "pkg-check --skip-import"}, "engines": {"node": ">=v18"}, "repository": {"type": "git", "url": "https://github.com/conventional-changelog/commitlint.git", "directory": "@commitlint/config-validator"}, "bugs": {"url": "https://github.com/conventional-changelog/commitlint/issues"}, "homepage": "https://commitlint.js.org/", "keywords": ["conventional-changelog", "commitlint", "library", "core"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@commitlint/utils": "^19.8.1"}, "dependencies": {"@commitlint/types": "^19.8.1", "ajv": "^8.11.0"}, "gitHead": "3c302008cabeb0b08cd246b2417a51a9d745a918"}