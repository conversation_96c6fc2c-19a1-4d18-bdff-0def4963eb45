# Installation
> `npm install --save @types/conventional-commits-parser`

# Summary
This package contains type definitions for conventional-commits-parser (https://github.com/conventional-changelog/conventional-changelog/tree/master/packages/conventional-commits-parser#readme).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/conventional-commits-parser.

### Additional Details
 * Last updated: Mon, 25 Nov 2024 22:35:05 GMT
 * Dependencies: [@types/node](https://npmjs.com/package/@types/node)

# Credits
These definitions were written by [<PERSON>](https://github.com/JasonHK).
