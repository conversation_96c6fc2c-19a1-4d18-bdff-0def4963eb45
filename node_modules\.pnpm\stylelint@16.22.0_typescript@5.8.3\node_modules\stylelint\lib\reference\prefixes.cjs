// NOTICE: This file is generated by Rollup. To modify it,
// please instead edit the ESM counterpart and rebuild with Rollup (npm run build).
'use strict';

/**
 * used by value-no-vendor-prefix and selector-no-vendor-prefix
 * i.e. this list is deliberately not exhaustive
 *
 * @see https://www.w3.org/TR/CSS22/syndata.html#vendor-keyword-history
 * @type {ReadonlySet<string>}
 */
const prefixes = new Set([
	'-webkit-',
	'-moz-',
	'-ms-',
	'-o-',
	'-xv-',
	'-apple-',
	'-wap-',
	'-khtml-',
]);

exports.prefixes = prefixes;
