{"version": 3, "file": "to-case.js", "sourceRoot": "", "sources": ["../src/to-case.ts"], "names": [], "mappings": "AACA,OAAO,SAAS,MAAM,kBAAkB,CAAC;AACzC,OAAO,SAAS,MAAM,kBAAkB,CAAC;AACzC,OAAO,SAAS,MAAM,kBAAkB,CAAC;AACzC,OAAO,UAAU,MAAM,mBAAmB,CAAC;AAC3C,OAAO,SAAS,MAAM,kBAAkB,CAAC;AAEzC,MAAM,CAAC,OAAO,UAAU,MAAM,CAAC,KAAa,EAAE,MAAsB;IACnE,QAAQ,MAAM,EAAE,CAAC;QAChB,KAAK,YAAY;YAChB,OAAO,SAAS,CAAC,KAAK,CAAC,CAAC;QACzB,KAAK,YAAY;YAChB,OAAO,SAAS,CAAC,KAAK,CAAC,CAAC;QACzB,KAAK,YAAY;YAChB,OAAO,SAAS,CAAC,KAAK,CAAC,CAAC;QACzB,KAAK,aAAa;YACjB,OAAO,UAAU,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;QACrC,KAAK,YAAY;YAChB,OAAO,SAAS,CAAC,KAAK,CAAC,CAAC;QACzB,KAAK,YAAY,CAAC;QAClB,KAAK,WAAW;YACf,OAAO,KAAK,CAAC,WAAW,EAAE,CAAC;QAC5B,KAAK,eAAe,CAAC;QACrB,KAAK,cAAc;YAClB,OAAO,UAAU,CAAC,KAAK,CAAC,CAAC;QAC1B,KAAK,YAAY,CAAC;QAClB,KAAK,WAAW,CAAC;QACjB,KAAK,WAAW,EAAE,qCAAqC;YACtD,OAAO,KAAK,CAAC,WAAW,EAAE,CAAC;QAC5B;YACC,MAAM,IAAI,SAAS,CAAC,iCAAiC,MAAM,GAAG,CAAC,CAAC;IAClE,CAAC;AACF,CAAC"}