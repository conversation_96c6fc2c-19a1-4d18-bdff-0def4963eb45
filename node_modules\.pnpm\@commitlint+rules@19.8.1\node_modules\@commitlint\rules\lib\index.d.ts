declare const _default: {
    "body-case": import("@commitlint/types").SyncRule<import("@commitlint/types").TargetCaseType | import("@commitlint/types").TargetCaseType[]>;
    "body-empty": import("@commitlint/types").SyncRule;
    "body-full-stop": import("@commitlint/types").SyncRule<string>;
    "body-leading-blank": import("@commitlint/types").SyncRule;
    "body-max-length": import("@commitlint/types").SyncRule<number>;
    "body-max-line-length": import("@commitlint/types").SyncRule<number>;
    "body-min-length": import("@commitlint/types").SyncRule<number>;
    "footer-empty": import("@commitlint/types").SyncRule;
    "footer-leading-blank": import("@commitlint/types").SyncRule;
    "footer-max-length": import("@commitlint/types").SyncRule<number>;
    "footer-max-line-length": import("@commitlint/types").SyncRule<number>;
    "footer-min-length": import("@commitlint/types").SyncRule<number>;
    "header-case": import("@commitlint/types").SyncRule<import("@commitlint/types").TargetCaseType | import("@commitlint/types").TargetCaseType[]>;
    "header-full-stop": import("@commitlint/types").SyncRule<string>;
    "header-max-length": import("@commitlint/types").SyncRule<number>;
    "header-min-length": import("@commitlint/types").SyncRule<number>;
    "header-trim": import("@commitlint/types").SyncRule;
    "references-empty": import("@commitlint/types").SyncRule;
    "scope-case": import("@commitlint/types").SyncRule<import("@commitlint/types").TargetCaseType | import("@commitlint/types").TargetCaseType[]>;
    "scope-empty": import("@commitlint/types").SyncRule;
    "scope-enum": import("@commitlint/types").SyncRule<string[]>;
    "scope-max-length": import("@commitlint/types").SyncRule<number>;
    "scope-min-length": import("@commitlint/types").SyncRule<number>;
    "signed-off-by": import("@commitlint/types").SyncRule<string>;
    "subject-case": import("@commitlint/types").SyncRule<import("@commitlint/types").TargetCaseType | import("@commitlint/types").TargetCaseType[]>;
    "subject-empty": import("@commitlint/types").SyncRule;
    "subject-full-stop": import("@commitlint/types").SyncRule<string>;
    "subject-max-length": import("@commitlint/types").SyncRule<number>;
    "subject-min-length": import("@commitlint/types").SyncRule<number>;
    "subject-exclamation-mark": import("@commitlint/types").SyncRule;
    "trailer-exists": import("@commitlint/types").SyncRule<string>;
    "type-case": import("@commitlint/types").SyncRule<import("@commitlint/types").TargetCaseType | import("@commitlint/types").TargetCaseType[]>;
    "type-empty": import("@commitlint/types").SyncRule;
    "type-enum": import("@commitlint/types").SyncRule<string[]>;
    "type-max-length": import("@commitlint/types").SyncRule<number>;
    "type-min-length": import("@commitlint/types").SyncRule<number>;
};
export default _default;
//# sourceMappingURL=index.d.ts.map