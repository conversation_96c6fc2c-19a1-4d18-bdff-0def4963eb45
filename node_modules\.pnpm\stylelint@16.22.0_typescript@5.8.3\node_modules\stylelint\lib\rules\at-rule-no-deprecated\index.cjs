// NOTICE: This file is generated by Rollup. To modify it,
// please instead edit the ESM counterpart and rebuild with Rollup (npm run build).
'use strict';

const postcss = require('postcss');
const validateTypes = require('../../utils/validateTypes.cjs');
const atKeywords = require('../../reference/atKeywords.cjs');
const isStandardSyntaxAtRule = require('../../utils/isStandardSyntaxAtRule.cjs');
const optionsMatches = require('../../utils/optionsMatches.cjs');
const report = require('../../utils/report.cjs');
const ruleMessages = require('../../utils/ruleMessages.cjs');
const validateOptions = require('../../utils/validateOptions.cjs');

const ruleName = 'at-rule-no-deprecated';

const messages = ruleMessages(ruleName, {
	rejected: (atRule) => `Unexpected deprecated at-rule "${atRule}"`,
});

const meta = {
	url: 'https://stylelint.io/user-guide/rules/at-rule-no-deprecated',
	fixable: true,
};

/** @type {import('stylelint').CoreRules[ruleName]} */
const rule = (primary, secondaryOptions) => {
	return (root, result) => {
		const validOptions = validateOptions(
			result,
			ruleName,
			{ actual: primary },
			{
				actual: secondaryOptions,
				possible: {
					ignoreAtRules: [validateTypes.isString, validateTypes.isRegExp],
				},
				optional: true,
			},
		);

		if (!validOptions) return;

		root.walkAtRules((atRule) => {
			if (!isStandardSyntaxAtRule(atRule)) return;

			const { name } = atRule;
			const normalizedName = name.toLowerCase();

			if (optionsMatches(secondaryOptions, 'ignoreAtRules', name)) return;

			if (!atKeywords.deprecatedAtKeywords.has(normalizedName)) return;

			const atNestFixer = () => {
				const styleRule = postcss.rule({
					selector: atRule.params,
					source: atRule.source,
				});

				styleRule.append(atRule.nodes);

				atRule.replaceWith(styleRule);
			};

			const fix = normalizedName === 'nest' ? atNestFixer : undefined;

			const atName = `@${name}`;

			report({
				message: messages.rejected,
				messageArgs: [atName],
				node: atRule,
				ruleName,
				result,
				index: 0,
				endIndex: atName.length,
				fix: {
					apply: fix,
					node: atRule.parent,
				},
			});
		});
	};
};

rule.ruleName = ruleName;
rule.messages = messages;
rule.meta = meta;

module.exports = rule;
