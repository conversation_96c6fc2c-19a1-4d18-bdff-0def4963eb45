// NOTICE: This file is generated by Rollup. To modify it,
// please instead edit the ESM counterpart and rebuild with Rollup (npm run build).
'use strict';

const constants = require('../constants.cjs');

/**
 * @import {Severity} from 'stylelint'
 *
 * @param {Severity} severity
 * @param {Record<Severity, number>} counts
 * @returns {void}
 */
function calcSeverityCounts(severity, counts) {
	switch (severity) {
		case constants.SEVERITY_ERROR:
			counts.error += 1;
			break;
		case constants.SEVERITY_WARNING:
			counts.warning += 1;
			break;
		default:
			throw new Error(`Unknown severity: "${severity}"`);
	}
}

module.exports = calcSeverityCounts;
