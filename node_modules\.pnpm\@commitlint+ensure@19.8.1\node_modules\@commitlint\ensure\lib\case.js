import toCase from "./to-case.js";
export default ensureCase;
function ensureCase(raw = "", target = "lowercase") {
    // We delete any content together with quotes because he can contains proper names (example `refactor: `<PERSON><PERSON><PERSON>` configuration`).
    // We need trim string because content with quotes can be at the beginning or end of a line
    const input = String(raw)
        .replace(/`.*?`|".*?"|'.*?'/g, "")
        .trim();
    const transformed = toCase(input, target);
    if (transformed === "" || transformed.match(/^\d/)) {
        return true;
    }
    return transformed === input;
}
//# sourceMappingURL=case.js.map