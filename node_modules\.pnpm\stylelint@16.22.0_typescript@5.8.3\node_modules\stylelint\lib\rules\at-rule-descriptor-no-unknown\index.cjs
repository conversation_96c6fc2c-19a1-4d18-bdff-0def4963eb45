// NOTICE: This file is generated by Rollup. To modify it,
// please instead edit the ESM counterpart and rebuild with Rollup (npm run build).
'use strict';

const properties = require('../../reference/properties.cjs');
const regexes = require('../../utils/regexes.cjs');
const getLexer = require('../../utils/getLexer.cjs');
const typeGuards = require('../../utils/typeGuards.cjs');
const isStandardSyntaxAtRule = require('../../utils/isStandardSyntaxAtRule.cjs');
const isStandardSyntaxDeclaration = require('../../utils/isStandardSyntaxDeclaration.cjs');
const atKeywords = require('../../reference/atKeywords.cjs');
const report = require('../../utils/report.cjs');
const ruleMessages = require('../../utils/ruleMessages.cjs');
const validateOptions = require('../../utils/validateOptions.cjs');

const ruleName = 'at-rule-descriptor-no-unknown';

const messages = ruleMessages(ruleName, {
	rejected: (atRule, descriptor) =>
		`Unexpected unknown descriptor "${descriptor}" for at-rule "${atRule}"`,
});

const meta = {
	url: 'https://stylelint.io/user-guide/rules/at-rule-descriptor-no-unknown',
};

/** @type {import('stylelint').CoreRules[ruleName]} */
const rule = (primary, _, context) => {
	return (root, result) => {
		const validOptions = validateOptions(result, ruleName, { actual: primary });

		if (!validOptions) {
			return;
		}

		root.walkAtRules(regexes.atRuleRegexes.unsupportedNestingNames, (atRule) => {
			if (!isStandardSyntaxAtRule(atRule)) return;

			atRule.walkDecls((decl) => {
				if (!isStandardSyntaxDeclaration(decl)) return;

				const { prop, value, parent } = decl;

				if (isPageContextPropertyInPageAtRule(atRule, prop)) return;

				if (
					parent &&
					typeGuards.isAtRule(parent) &&
					isMarginContextPropertyInMarginAtRule(atRule, prop, parent)
				)
					return;

				const lexer = getLexer(context);
				const { error } = lexer.matchAtruleDescriptor(atRule.name, prop, value);

				if (!error) return;

				if (error.name !== 'SyntaxReferenceError') return;

				if (!error.message.startsWith('Unknown at-rule descriptor')) return;

				const atName = `@${atRule.name}`;

				report({
					message: messages.rejected,
					messageArgs: [atName, prop],
					node: decl,
					index: 0,
					endIndex: prop.length,
					ruleName,
					result,
				});
			});
		});
	};
};

/**
 * @param {import('postcss').AtRule} atRule
 * @param {string} prop
 * @param {import('postcss').AtRule} parent
 */
function isMarginContextPropertyInMarginAtRule(atRule, prop, parent) {
	return (
		atRule.name.toLowerCase() === 'page' &&
		atKeywords.pageMarginAtKeywords.has(parent.name.toLowerCase()) &&
		properties.marginContextProperties.has(prop)
	);
}

/**
 * @param {import('postcss').AtRule} atRule
 * @param {string} prop
 */
function isPageContextPropertyInPageAtRule(atRule, prop) {
	return atRule.name.toLowerCase() === 'page' && properties.pageContextProperties.has(prop);
}

rule.ruleName = ruleName;
rule.messages = messages;
rule.meta = meta;

module.exports = rule;
