{"version": 3, "file": "load-parser-opts.js", "sourceRoot": "", "sources": ["../../src/utils/load-parser-opts.ts"], "names": [], "mappings": "AAIA,SAAS,YAAY,CAAC,GAAY;IACjC,OAAO,OAAO,CAAC,GAAG,CAAC,IAAI,OAAO,GAAG,KAAK,QAAQ,CAAC,CAAC,2BAA2B;AAC5E,CAAC;AAED,SAAS,oBAAoB,CAC5B,GAAM;IAMN,OAAO,OAAO,GAAG,CAAC,UAAU,KAAK,UAAU,CAAC;AAC7C,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,cAAc,CACnC,aAIY;IAEZ,IAAI,OAAO,aAAa,KAAK,UAAU,EAAE,CAAC;QACzC,OAAO,cAAc,CAAC,aAAa,EAAE,CAAC,CAAC;IACxC,CAAC;IAED,IAAI,CAAC,aAAa,IAAI,OAAO,aAAa,KAAK,QAAQ,EAAE,CAAC;QACzD,OAAO,SAAS,CAAC;IAClB,CAAC;IACD,4CAA4C;IAC5C,MAAM,MAAM,GAAG,MAAM,aAAa,CAAC;IAEnC,iCAAiC;IACjC,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;QACxB,OAAO,MAAM,CAAC;IACf,CAAC;IAED,mFAAmF;IACnF,IAAI,OAAO,MAAM,CAAC,UAAU,KAAK,QAAQ,EAAE,CAAC;QAC3C,kCAAkC;QAClC,MAAM,CAAC,UAAU,GAAG,MAAM,MAAM,CAAC,UAAU,CAAC;QAC5C,IACC,YAAY,CAAC,MAAM,CAAC,UAAU,CAAC;YAC/B,YAAY,CAAC,MAAM,CAAC,UAAU,CAAC,UAAU,CAAC,EACzC,CAAC;YACF,MAAM,CAAC,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC,UAAU,CAAC;QAClD,CAAC;QACD,OAAO,MAAM,CAAC;IACf,CAAC;IAED,kCAAkC;IAClC,IACC,oBAAoB,CAAC,MAAM,CAAC;QAC5B,OAAO,MAAM,CAAC,IAAI,KAAK,QAAQ;QAC/B,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,yBAAyB,CAAC,EAChD,CAAC;QACF,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC9B,MAAM,MAAM,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC,CAAQ,EAAE,IAAI,EAAE,EAAE;gBACnD,OAAO,CAAC;oBACP,GAAG,MAAM;oBACT,UAAU,EAAE,IAAI,EAAE,UAAU;iBAC5B,CAAC,CAAC;YACJ,CAAC,CAAC,CAAC;YAEH,2EAA2E;YAC3E,0GAA0G;YAC1G,IAAI,MAAM,EAAE,CAAC;gBACZ,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE;oBACrC,OAAO,CAAC;wBACP,GAAG,MAAM;wBACT,UAAU,EAAE,IAAI,EAAE,UAAU;qBAC5B,CAAC,CAAC;gBACJ,CAAC,CAAC,CAAC;YACJ,CAAC;YACD,OAAO;QACR,CAAC,CAAC,CAAC;IACJ,CAAC;IAED,OAAO,MAAM,CAAC;AACf,CAAC"}