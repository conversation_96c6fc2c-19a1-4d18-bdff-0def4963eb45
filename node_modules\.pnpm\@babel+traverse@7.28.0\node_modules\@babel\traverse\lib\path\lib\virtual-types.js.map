{"version": 3, "names": ["ReferencedIdentifier", "exports", "ReferencedMemberExpression", "BindingIdentifier", "Statement", "Expression", "<PERSON><PERSON>", "Referenced", "BlockScoped", "Var", "User", "Generated", "Pure", "Flow", "RestProperty", "SpreadProperty", "ExistentialTypeParam", "NumericLiteralTypeAnnotation", "ForAwaitStatement"], "sources": ["../../../src/path/lib/virtual-types.ts"], "sourcesContent": ["import type * as t from \"@babel/types\";\n\nexport interface VirtualTypeAliases {\n  BindingIdentifier: t.Identifier;\n  BlockScoped: t.Node;\n  ExistentialTypeParam: t.ExistsTypeAnnotation;\n  Expression: t.Expression;\n  Flow: t.Flow | t.ImportDeclaration | t.ExportDeclaration | t.ImportSpecifier;\n  ForAwaitStatement: t.ForOfStatement;\n  Generated: t.Node;\n  NumericLiteralTypeAnnotation: t.NumberLiteralTypeAnnotation;\n  Pure: t.Node;\n  Referenced: t.Node;\n  ReferencedIdentifier: t.Identifier | t.JSXIdentifier;\n  ReferencedMemberExpression: t.MemberExpression;\n  RestProperty: t.RestElement;\n  Scope: t.Scopable | t.Pattern;\n  SpreadProperty: t.RestElement;\n  Statement: t.Statement;\n  User: t.Node;\n  Var: t.VariableDeclaration;\n}\n\ntype VirtualTypeMapping = readonly (t.Node[\"type\"] | keyof t.Aliases)[] | null;\n\nexport const ReferencedIdentifier: VirtualTypeMapping = [\n  \"Identifier\",\n  \"JSXIdentifier\",\n] as const;\n\nexport const ReferencedMemberExpression: VirtualTypeMapping = [\n  \"MemberExpression\",\n] as const;\n\nexport const BindingIdentifier: VirtualTypeMapping = [\"Identifier\"] as const;\n\nexport const Statement: VirtualTypeMapping = [\"Statement\"] as const;\n\nexport const Expression: VirtualTypeMapping = [\"Expression\"] as const;\n\nexport const Scope: VirtualTypeMapping = [\"Scopable\", \"Pattern\"] as const;\n\nexport const Referenced: VirtualTypeMapping = null;\n\nexport const BlockScoped: VirtualTypeMapping = null;\n\nexport const Var: VirtualTypeMapping = [\"VariableDeclaration\"];\n\nexport const User: VirtualTypeMapping = null;\n\nexport const Generated: VirtualTypeMapping = null;\n\nexport const Pure: VirtualTypeMapping = null;\n\nexport const Flow: VirtualTypeMapping = [\n  \"Flow\",\n  \"ImportDeclaration\",\n  \"ExportDeclaration\",\n  \"ImportSpecifier\",\n] as const;\n\n// TODO: 7.0 Backwards Compat\nexport const RestProperty: VirtualTypeMapping = [\"RestElement\"] as const;\n\nexport const SpreadProperty: VirtualTypeMapping = [\"RestElement\"] as const;\n\nexport const ExistentialTypeParam: VirtualTypeMapping = [\n  \"ExistsTypeAnnotation\",\n] as const;\n\nexport const NumericLiteralTypeAnnotation: VirtualTypeMapping = [\n  \"NumberLiteralTypeAnnotation\",\n] as const;\n\nexport const ForAwaitStatement: VirtualTypeMapping = [\n  \"ForOfStatement\",\n] as const;\n"], "mappings": ";;;;;;AAyBO,MAAMA,oBAAwC,GAAAC,OAAA,CAAAD,oBAAA,GAAG,CACtD,YAAY,EACZ,eAAe,CACP;AAEH,MAAME,0BAA8C,GAAAD,OAAA,CAAAC,0BAAA,GAAG,CAC5D,kBAAkB,CACV;AAEH,MAAMC,iBAAqC,GAAAF,OAAA,CAAAE,iBAAA,GAAG,CAAC,YAAY,CAAU;AAErE,MAAMC,SAA6B,GAAAH,OAAA,CAAAG,SAAA,GAAG,CAAC,WAAW,CAAU;AAE5D,MAAMC,UAA8B,GAAAJ,OAAA,CAAAI,UAAA,GAAG,CAAC,YAAY,CAAU;AAE9D,MAAMC,KAAyB,GAAAL,OAAA,CAAAK,KAAA,GAAG,CAAC,UAAU,EAAE,SAAS,CAAU;AAElE,MAAMC,UAA8B,GAAAN,OAAA,CAAAM,UAAA,GAAG,IAAI;AAE3C,MAAMC,WAA+B,GAAAP,OAAA,CAAAO,WAAA,GAAG,IAAI;AAE5C,MAAMC,GAAuB,GAAAR,OAAA,CAAAQ,GAAA,GAAG,CAAC,qBAAqB,CAAC;AAEvD,MAAMC,IAAwB,GAAAT,OAAA,CAAAS,IAAA,GAAG,IAAI;AAErC,MAAMC,SAA6B,GAAAV,OAAA,CAAAU,SAAA,GAAG,IAAI;AAE1C,MAAMC,IAAwB,GAAAX,OAAA,CAAAW,IAAA,GAAG,IAAI;AAErC,MAAMC,IAAwB,GAAAZ,OAAA,CAAAY,IAAA,GAAG,CACtC,MAAM,EACN,mBAAmB,EACnB,mBAAmB,EACnB,iBAAiB,CACT;AAGH,MAAMC,YAAgC,GAAAb,OAAA,CAAAa,YAAA,GAAG,CAAC,aAAa,CAAU;AAEjE,MAAMC,cAAkC,GAAAd,OAAA,CAAAc,cAAA,GAAG,CAAC,aAAa,CAAU;AAEnE,MAAMC,oBAAwC,GAAAf,OAAA,CAAAe,oBAAA,GAAG,CACtD,sBAAsB,CACd;AAEH,MAAMC,4BAAgD,GAAAhB,OAAA,CAAAgB,4BAAA,GAAG,CAC9D,6BAA6B,CACrB;AAEH,MAAMC,iBAAqC,GAAAjB,OAAA,CAAAiB,iBAAA,GAAG,CACnD,gBAAgB,CACR", "ignoreList": []}