import { existsSync, readFileSync } from "node:fs";
import path from "node:path";
import { cosmiconfig, defaultLoadersSync, defaultLoaders, } from "cosmiconfig";
import { TypeScriptLoader } from "cosmiconfig-typescript-loader";
const moduleName = "commitlint";
const searchStrategy = "global";
export async function loadConfig(cwd, configPath) {
    let tsLoaderInstance;
    const tsLoader = (...args) => {
        if (!tsLoaderInstance) {
            tsLoaderInstance = TypeScriptLoader();
        }
        return tsLoaderInstance(...args);
    };
    // If dynamic await is supported (Node >= v20.8.0) or directory uses ESM, support
    // async js/cjs loaders (dynamic import). Otherwise, use synchronous js/cjs loaders.
    const loaders = isDynamicAwaitSupported() || isEsmModule(cwd)
        ? defaultLoaders
        : defaultLoadersSync;
    const explorer = cosmiconfig(moduleName, {
        searchStrategy,
        searchPlaces: [
            // cosmiconfig overrides default searchPlaces if any new search place is added (For e.g. `*.ts` files),
            // we need to manually merge default searchPlaces from https://github.com/davidtheclark/cosmiconfig#searchplaces
            "package.json",
            "package.yaml",
            `.${moduleName}rc`,
            `.${moduleName}rc.json`,
            `.${moduleName}rc.yaml`,
            `.${moduleName}rc.yml`,
            `.${moduleName}rc.js`,
            `.${moduleName}rc.cjs`,
            `.${moduleName}rc.mjs`,
            `${moduleName}.config.js`,
            `${moduleName}.config.cjs`,
            `${moduleName}.config.mjs`,
            // files supported by TypescriptLoader
            `.${moduleName}rc.ts`,
            `.${moduleName}rc.cts`,
            `${moduleName}.config.ts`,
            `${moduleName}.config.cts`,
        ],
        loaders: {
            ".ts": tsLoader,
            ".cts": tsLoader,
            ".cjs": loaders[".cjs"],
            ".js": loaders[".js"],
        },
    });
    const explicitPath = configPath ? path.resolve(cwd, configPath) : undefined;
    const explore = explicitPath ? explorer.load : explorer.search;
    const searchPath = explicitPath ? explicitPath : cwd;
    const local = await explore(searchPath);
    if (local) {
        return local;
    }
    return null;
}
// See the following issues for more context, contributing to failing Jest tests:
//  - Issue: https://github.com/nodejs/node/issues/40058
//  - Resolution: https://github.com/nodejs/node/pull/48510 (Node v20.8.0)
export const isDynamicAwaitSupported = () => {
    const [major, minor] = process.version
        .replace("v", "")
        .split(".")
        .map((val) => parseInt(val));
    return major >= 20 && minor >= 8;
};
// Is the given directory set up to use ESM (ECMAScript Modules)?
export const isEsmModule = (cwd) => {
    const packagePath = path.join(cwd, "package.json");
    if (!existsSync(packagePath)) {
        return false;
    }
    const packageJSON = readFileSync(packagePath, { encoding: "utf-8" });
    return JSON.parse(packageJSON)?.type === "module";
};
//# sourceMappingURL=load-config.js.map