{"name": "@types/conventional-commits-parser", "version": "5.0.1", "description": "TypeScript definitions for conventional-commits-parser", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/conventional-commits-parser", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "JasonHK", "url": "https://github.com/JasonHK"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/conventional-commits-parser"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "02ad480fea4412ce1e31ee3828a5006bf9a339132437f12a613901f4b04283a4", "typeScriptVersion": "5.0"}