{"version": 3, "file": "formatErrors.js", "sourceRoot": "", "sources": ["../src/formatErrors.ts"], "names": [], "mappings": "AAEA;;;;;GAKG;AACH,MAAM,UAAU,YAAY,CAAC,MAAqB;IACjD,OAAO,MAAM;SACX,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;QACd,IACC,KAAK,CAAC,OAAO,KAAK,sBAAsB;YACxC,oBAAoB,IAAI,KAAK,CAAC,MAAM,EACnC,CAAC;YACF,MAAM,qBAAqB,GAAG,KAAK,CAAC,YAAY,CAAC,MAAM;gBACtD,CAAC,CAAC,GAAG,KAAK,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,kBAAkB,EAAE;gBACrE,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,kBAAkB,CAAC;YAEnC,OAAO,kCAAkC,qBAAqB,GAAG,CAAC;QACnE,CAAC;QACD,IAAI,KAAK,CAAC,OAAO,KAAK,MAAM,EAAE,CAAC;YAC9B,MAAM,cAAc,GAAG,KAAK,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YACnD,IAAI,CAAC,cAAc,EAAE,CAAC;gBACrB,OAAO,+BAA+B,KAAK,CAAC,OAAO,EAAE,CAAC;YACvD,CAAC;YACD,OAAO,aAAa,cAAc,0BAA0B,KAAK,CAAC,OAAO,EAAE,CAAC;QAC7E,CAAC;QACD,MAAM,KAAK,GACV,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC,KAAK,GAAG;YAC7B,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;YAC7B,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,QAAQ,CAAC;QACpC,IAAI,KAAK,CAAC,OAAO,KAAK,QAAQ,EAAE,CAAC;YAChC,OAAO,IAAI,KAAK,iBAAiB,KAAK,CAAC,MAAM,YAAY,IAAI,CAAC,SAAS,CACtE,KAAK,CAAC,IAAI,CACV,EAAE,CAAC;QACL,CAAC;QAED,OAAO,IAAI,KAAK,KAAK,KAAK,CAAC,OAAO,YAAY,IAAI,CAAC,SAAS,CAC3D,KAAK,CAAC,IAAI,CACV,EAAE,CAAC;IACL,CAAC,CAAC;SACD,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,OAAO,KAAK,CAAC;SACrC,IAAI,CAAC,EAAE,CAAC,CAAC;AACZ,CAAC"}